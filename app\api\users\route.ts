import { NextResponse } from "next/server"

export async function GET() {
  // Simulate database fetch delay
  await new Promise((resolve) => setTimeout(resolve, 800))

  const users = [
    {
      id: 1,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "Admin",
      status: "active",
      lastActive: "Just now",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 2,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "Manager",
      status: "active",
      lastActive: "2 hours ago",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 3,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "Technician",
      status: "active",
      lastActive: "Yesterday",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 4,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "Analyst",
      status: "inactive",
      lastActive: "3 days ago",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 5,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "Technician",
      status: "active",
      lastActive: "1 hour ago",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 6,
      name: "Emily Zhang",
      email: "<EMAIL>",
      role: "Manager",
      status: "inactive",
      lastActive: "1 week ago",
      avatar: "/placeholder.svg?height=40&width=40",
    },
  ]

  return NextResponse.json({ users })
}
