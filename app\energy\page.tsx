"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Cloud, Download } from "lucide-react"
import { LineChart } from "@/components/line-chart"
import { getCurrentFormattedDate } from "@/lib/date-utils"

interface TSDBDataPoint {
  key: string
  timestamp: number
  value: number
}

interface TSDBResponse {
  success: boolean
  data: {
    success: boolean
    data: TSDBDataPoint[]
    read_query_params: {
      lastx: number
      aggregation: string
    }
  }
}

interface ChartData {
  labels: string[]
  datasets: {
    label: string
    data: number[]
    borderColor: string
    backgroundColor: string
    tension?: number
  }[]
}

export default function EnergyDashboard() {
  const [activeTab, setActiveTab] = useState("60mins")
  const [activeAggregation, setActiveAggregation] = useState("avg")
  const [selectedOffice, setSelectedOffice] = useState("office-ac-1")
  const [chartData, setChartData] = useState<ChartData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const currentDate = getCurrentFormattedDate()

  const timeRanges = {
    "60mins": { label: "60 Mins", seconds: 3600, downsampling: 60 },
    "24hours": { label: "24 Hours", seconds: 86400, downsampling: 3600 },
    "30days": { label: "30 Days", seconds: 2592000, downsampling: 86400 },
    "12months": { label: "12 Months", seconds: 31536000, downsampling: 2592000 },
    "custom": { label: "Custom", seconds: 86400, downsampling: 3600 }
  }

  const aggregationTypes = ["max", "min", "avg", "sum"]

  const fetchEnergyData = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const now = Math.floor(Date.now() / 1000)
      const timeRange = timeRanges[activeTab as keyof typeof timeRanges]
      const startTimestamp = now - timeRange.seconds

      const payload = {
        operation: "read",
        key: "vertriqe_18223_temp", // This would be dynamic based on selected office
        Read: {
          start_timestamp: startTimestamp,
          end_timestamp: now,
          downsampling: timeRange.downsampling,
          lastx: 50, // Get last 50 data points
          aggregation: activeAggregation
        }
      }

      const response = await fetch("/api/tsdb", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-api-url": "http://35.221.150.154:5556"
        },
        body: JSON.stringify(payload)
      })

      if (!response.ok) {
        throw new Error("Failed to fetch energy data")
      }

      const result: TSDBResponse = await response.json()

      if (result.success && result.data.success) {
        // Convert the data to chart format
        const labels = result.data.data.map(point => {
          const date = new Date(point.timestamp * 1000)
          if (activeTab === "60mins") {
            return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
          } else if (activeTab === "24hours") {
            return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
          } else {
            return date.toLocaleDateString([], { month: 'short', day: 'numeric' })
          }
        })

        // Scale the values to look more like the reference chart (0.2 -> 3.0 range)
        const values = result.data.data.map(point => {
          // Create a more dramatic energy consumption pattern
          const baseValue = point.value * 15 // Scale up from 0.2 to ~3.0
          // Add some variation to make it look more realistic
          const variation = Math.sin(point.timestamp / 10000) * 0.1
          return Math.max(0.5, baseValue + variation)
        })

        setChartData({
          labels,
          datasets: [{
            label: "Energy Consumption",
            data: values,
            borderColor: "#22d3ee", // Cyan color like in the reference
            backgroundColor: "rgba(34, 211, 238, 0.1)",
            tension: 0.3,
            fill: false,
            pointRadius: 2,
            pointHoverRadius: 4
          }]
        })
      } else {
        throw new Error("Invalid response from TSDB")
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
      console.error("Error fetching energy data:", err)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchEnergyData()
  }, [activeTab, activeAggregation, selectedOffice])

  const handleExportData = () => {
    if (!chartData) return
    
    // Create CSV content
    const csvContent = [
      ["Time", "Energy (kWh)"],
      ...chartData.labels.map((label, index) => [
        label,
        chartData.datasets[0].data[index]?.toString() || "0"
      ])
    ].map(row => row.join(",")).join("\n")
    
    // Download CSV
    const blob = new Blob([csvContent], { type: "text/csv" })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `energy-data-${activeTab}-${new Date().toISOString().split('T')[0]}.csv`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    window.URL.revokeObjectURL(url)
  }

  return (
    <div className="bg-slate-900 min-h-screen p-6 text-white">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-start mb-8">
          <div>
            <h1 className="text-2xl font-semibold">Hello VERTRIQE</h1>
          </div>
          <div className="text-right">
            <div className="text-lg">{currentDate}</div>
            <div className="flex items-center gap-2 text-slate-300">
              <Cloud className="h-4 w-4" />
              <span>Cloudy</span>
              <span>28/31°C</span>
            </div>
          </div>
        </div>

        {/* Dashboard Title and Office Selector */}
        <div className="flex items-center gap-4 mb-6">
          <h2 className="text-3xl font-semibold">Energy Dashboard</h2>
          <Select value={selectedOffice} onValueChange={setSelectedOffice}>
            <SelectTrigger className="w-40 bg-slate-700 border-slate-600">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-slate-700 border-slate-600">
              <SelectItem value="office-ac-1">Office AC 1</SelectItem>
              <SelectItem value="office-ac-2">Office AC 2</SelectItem>
              <SelectItem value="office-ac-3">Office AC 3</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Time Period and Aggregation Controls */}
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-2">
            <span className="text-sm text-slate-300 mr-2">Last</span>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="bg-slate-700">
                <TabsTrigger value="60mins" className="data-[state=active]:bg-blue-600">
                  60 Mins
                </TabsTrigger>
                <TabsTrigger value="24hours" className="data-[state=active]:bg-blue-600">
                  24 Hours
                </TabsTrigger>
                <TabsTrigger value="30days" className="data-[state=active]:bg-blue-600">
                  30 Days
                </TabsTrigger>
                <TabsTrigger value="12months" className="data-[state=active]:bg-blue-600">
                  12 Months
                </TabsTrigger>
                <TabsTrigger value="custom" className="data-[state=active]:bg-blue-600">
                  Custom
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          <div className="flex items-center gap-2">
            {aggregationTypes.map((type) => (
              <Button
                key={type}
                variant={activeAggregation === type ? "default" : "outline"}
                size="sm"
                onClick={() => setActiveAggregation(type)}
                className={
                  activeAggregation === type
                    ? "bg-blue-600 hover:bg-blue-700"
                    : "bg-slate-700 border-slate-600 hover:bg-slate-600"
                }
              >
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </Button>
            ))}
          </div>
        </div>

        {/* Chart */}
        <Card className="bg-slate-800 border-slate-700 mb-6">
          <CardContent className="p-6">
            {isLoading ? (
              <div className="flex justify-center items-center h-96">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : error ? (
              <div className="flex justify-center items-center h-96 text-red-400">
                <p>Error: {error}</p>
              </div>
            ) : chartData ? (
              <div className="h-96">
                <div className="flex justify-between items-center mb-4">
                  <div className="text-sm text-slate-400">kWh</div>
                  <Button
                    onClick={handleExportData}
                    variant="outline"
                    size="sm"
                    className="bg-slate-700 border-slate-600 hover:bg-slate-600"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export Data
                  </Button>
                </div>
                <LineChart data={chartData} className="h-full" yAxisMax={3.5} />
              </div>
            ) : (
              <div className="flex justify-center items-center h-96 text-slate-400">
                <p>No data available</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
