"use client"

import type React from "react"
import { usePathname } from "next/navigation"
import { Sidebar } from "@/components/sidebar"
import { ThemeProvider } from "@/components/theme-provider"
import { UserProvider } from "@/contexts/user-context"
import "@/app/globals.css"

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const isLoginPage = pathname === "/login"

  return (
    <html lang="en">
      <body className="bg-gray-100">
        <ThemeProvider attribute="class" defaultTheme="dark">
          {isLoginPage ? (
            // Login page without sidebar
            children
          ) : (
            // Protected pages with sidebar
            <UserProvider>
              <div className="flex h-screen">
                <Sidebar />
                <main className="flex-1 overflow-auto">{children}</main>
              </div>
            </UserProvider>
          )}
        </ThemeProvider>
      </body>
    </html>
  )
}
